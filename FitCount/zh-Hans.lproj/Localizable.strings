/* FitCount Chinese (Simplified) Localizations */

"tab.first.title" = "模块一";
"tab.middle.title" = "核心功能";
"tab.last.title" = "模块二";

"view.first.content" = "这是第一个模块的内容区域。";
"view.middle.content" = "这是核心功能模块的内容区域。";
"view.last.content" = "这是第二个模块的内容区域。";

"view.first.button.goToDetail" = "跳转到详情页";

// 详情页本地化
"view.first.detail.title" = "详情页面";
"view.first.detail.content" = "这是从第一个模块跳转过来的全屏详情页内容。";
"view.first.detail.navigationTitle" = "模块一详情";
"button.done" = "完成";

// FirstView本地化
"exercise" = "运动";
"exercise.selection.description" = "选择你想进行的运动,所有的数据仅存在您的手机上";
"exercise.situp" = "仰卧起坐";
"exercise.pullup" = "引体向上";
"exercise.pushup" = "俯卧撑";
"exercise.squat" = "深蹲";
"exercise.plank" = "平板支撑";
"error.prefix" = "错误: ";

// 相机权限提示
"camera.permission.title" = "需要相机权限";
"camera.permission.message" = "请前往设置开启相机权限以使用此功能";
"camera.permission.ok" = "确定";

// 设备方向和坐标转换相关
"orientation.portrait" = "竖屏";
"orientation.landscape.left" = "横屏左";
"orientation.landscape.right" = "横屏右";
"orientation.unknown" = "未知方向";
"pose.overlay.coordinate.transform" = "姿态坐标转换";
"pose.overlay.orientation.changed" = "设备方向已改变";