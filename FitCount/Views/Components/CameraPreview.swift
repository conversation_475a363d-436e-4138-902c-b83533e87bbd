import SwiftUI
import AVFoundation

// MARK: - 相机预览组件

/// 预览视图 - 用于显示相机预览的自定义UIView
class CameraPreviewView: UIView {
    
    /// 存储预览层引用
    var previewLayer: AVCaptureVideoPreviewLayer? {
        didSet {
            DebugLogger.debug("设置预览层")
            
            // 移除旧层
            if let oldLayer = oldValue {
                DebugLogger.debug("移除旧的预览层")
                oldLayer.removeFromSuperlayer()
            }
            
            // 设置新层
            if let newLayer = previewLayer {
                DebugLogger.debug("添加新的预览层到视图")
                
                // 配置预览层属性
                newLayer.videoGravity = .resizeAspectFill
                layer.addSublayer(newLayer)
                
                // 设置预览层框架以填充视图
                CATransaction.begin()
                CATransaction.setDisableActions(true) // 禁用动画
                newLayer.frame = bounds
                CATransaction.commit()
                
                // 检查预览层会话是否设置
                if let session = newLayer.session {
                    DebugLogger.debug("预览层关联的会话存在")
                    
                    // 检查会话是否正在运行
                    if session.isRunning {
                        DebugLogger.debug("预览层关联的会话正在运行")
                    } else {
                        DebugLogger.warning("预览层关联的会话未运行")
                    }
                } else {
                    DebugLogger.error("预览层没有关联会话！")
                }
                
                // 检查连接状态
                if let connection = newLayer.connection {
                    DebugLogger.debug("预览层连接状态: enabled=\(connection.isEnabled)")
                    
                    // 尝试启用连接
                    connection.isEnabled = true
                } else {
                    DebugLogger.warning("预览层没有连接")
                }
            } else {
                DebugLogger.debug("预览层被设置为nil")
            }
        }
    }
    
    // MARK: - 生命周期方法
    
    /// 视图布局改变时调用
    override func layoutSubviews() {
        super.layoutSubviews()
        DebugLogger.verbose("CameraPreviewView.layoutSubviews 被调用，bounds=\(bounds)")
        
        // 更新预览层框架以匹配视图边界
        CATransaction.begin()
        CATransaction.setDisableActions(true) // 禁用动画
        previewLayer?.frame = bounds
        CATransaction.commit()
        
        // 确保预览层仍在视图层次结构中
        if let layer = previewLayer, layer.superlayer == nil {
            DebugLogger.warning("预览层已从层次结构中移除，重新添加")
            self.layer.addSublayer(layer)
        }
    }
    
    /// 视图即将从视图层次结构中移除时调用
    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)
        
        if newSuperview == nil {
            DebugLogger.debug("CameraPreviewView将从视图层次结构中移除")
        } else {
            DebugLogger.debug("CameraPreviewView将添加到新的父视图")
        }
    }
}

// MARK: - SwiftUI包装器

/// SwiftUI视图代表 - 包装CameraPreviewView
struct CameraPreview: UIViewRepresentable {
    var previewLayer: AVCaptureVideoPreviewLayer
    
    // MARK: - UIViewRepresentable协议实现
    
    /// 创建UIView
    func makeUIView(context: Context) -> CameraPreviewView {
        DebugLogger.debug("CameraPreview.makeUIView被调用")
        
        let view = CameraPreviewView()
        view.backgroundColor = .black
        
        // 设置预览层
        view.previewLayer = previewLayer
        
        // 检查关联的会话
        DebugLogger.debug("预览层会话状态: \(previewLayer.session?.isRunning == true ? "正在运行" : "未运行")")
        
        return view
    }
    
    /// 更新UIView
    func updateUIView(_ uiView: CameraPreviewView, context: Context) {
        DebugLogger.debug("CameraPreview.updateUIView被调用")
        
        // 检查预览层是否相同
        if uiView.previewLayer !== previewLayer {
            DebugLogger.debug("预览层实例已更改，更新UIView")
            uiView.previewLayer = previewLayer
        } else {
            DebugLogger.verbose("预览层实例相同，无需更新")
        }
        
        // 无论如何都重新布局视图
        uiView.setNeedsLayout()
    }
    
    /// 视图将从层次结构中移除时调用
    static func dismantleUIView(_ uiView: CameraPreviewView, coordinator: ()) {
        DebugLogger.debug("CameraPreview.dismantleUIView被调用")
        uiView.previewLayer = nil
    }
}

// MARK: - 扩展：AVLayerVideoGravity 转换

/// 扩展：将AVLayerVideoGravity转换为UIView.ContentMode
extension AVLayerVideoGravity {
    var contentMode: UIView.ContentMode {
        switch self {
        case .resizeAspectFill:
            return .scaleAspectFill
        case .resizeAspect:
            return .scaleAspectFit
        case .resize:
            return .scaleToFill
        default:
            return .scaleAspectFill
        }
    }
}
