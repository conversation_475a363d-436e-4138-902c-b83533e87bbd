import SwiftUI
import AVFoundation
import Combine
import Foundation
import MediaPipeTasksVision
import CoreMedia

/// 相机管理器
/// 负责处理摄像头相关操作，包括会话管理、设备切换、权限处理等
class CameraManager: NSObject, ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var session = AVCaptureSession()
    @Published var output = AVCaptureVideoDataOutput()
    @Published var preview: AVCaptureVideoPreviewLayer?
    @Published var isUsingFrontCamera = false
    @Published var sessionError: String? = nil
    @Published var isSessionRunning = false
    @Published var isSessionInterrupted = false
    @Published var debugState: String = "初始化中" // 用于UI显示的状态
    
    // MARK: - 人体姿态检测相关
    
    /// 人体姿态叠加视图，用于绘制关键点
    @Published var poseOverlayView: PoseOverlayView?
    
    // MARK: - 私有属性
    
    /// 样本缓冲区处理队列
    let sampleBufferQueue = DispatchQueue(label: "sampleBufferQueue")
    
    /// 后台处理队列
     let backgroundQueue = DispatchQueue(label: "com.google.mediapipe.cameraController.backgroundQueue")
    
    /// 监听相机会话状态的通知观察者
    var sessionRunningObservation: NSKeyValueObservation?
    var notificationCenter: NotificationCenter
    var sessionQueue = DispatchQueue(label: "camera.session.queue")
    
    // MARK: - MediaPipe相关
    
    /// 相机数据服务代理
    weak var delegate: CameraFeedServiceDelegate?
    
    /// 姿态检测服务队列
    private let poseLandmarkerServiceQueue = DispatchQueue(
        label: "com.google.mediapipe.cameraController.poseLandmarkerServiceQueue",
        attributes: .concurrent)
    
    /// 姿态检测服务（线程安全访问）
    private var _poseLandmarkerService: PoseLandmarkerService?
     var fieldPoseLandmarkerService: PoseLandmarkerService? {
        get {
            poseLandmarkerServiceQueue.sync {
                return self._poseLandmarkerService
            }
        }
        set {
            poseLandmarkerServiceQueue.async(flags: .barrier) {
                self._poseLandmarkerService = newValue
            }
        }
    }
    
    // MARK: - 初始化
    
    override init() {
        DebugLogger.info("CameraManager初始化")
        self.notificationCenter = NotificationCenter.default
        super.init()
        
        // 创建人体姿态叠加视图
        self.poseOverlayView = PoseOverlayView()
        
        // 设置观察者和初始化姿态检测服务
        self.setupObservers()
        clearAndInitializePoseLandmarkerService()
    }
    
    deinit {
        DebugLogger.info("CameraManager销毁")
        self.removeObservers()
    }
    
    // MARK: - MediaPipe服务管理
    
    /// 清除姿态检测服务（会话中断时调用）
    private func clearPoseLandmarkerServiceOnSessionInterruption() {
        fieldPoseLandmarkerService = nil
    }
    
    /// 清除并重新初始化姿态检测服务
    @objc private func clearAndInitializePoseLandmarkerService() {
        fieldPoseLandmarkerService = nil
        fieldPoseLandmarkerService = PoseLandmarkerService
            .liveStreamPoseLandmarkerService(
                modelPath: InferenceConfigurationManager.sharedInstance.model.modelPath,
                numPoses: InferenceConfigurationManager.sharedInstance.numPoses,
                minPoseDetectionConfidence: InferenceConfigurationManager.sharedInstance.minPoseDetectionConfidence,
                minPosePresenceConfidence: InferenceConfigurationManager.sharedInstance.minPosePresenceConfidence,
                minTrackingConfidence: InferenceConfigurationManager.sharedInstance.minTrackingConfidence,
                liveStreamDelegate: self,
                delegate: InferenceConfigurationManager.sharedInstance.delegate)
    }
    
    // MARK: - 会话管理
    
    /// 设置并启动相机会话（完整流程）
    func setupAndStartSession() {
        DebugLogger.info("开始设置并启动相机")
        
        // 更新UI状态
        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在初始化相机..."
        }
        
        sessionQueue.async { [weak self] in
            guard let self = self else {
                DebugLogger.error("setupAndStartSession: self已释放")
                return
            }
            
            DebugLogger.debug("在sessionQueue中开始设置相机")
            
            // 检查当前会话状态
            if self.session.isRunning {
                DebugLogger.warning("会话已经在运行中，先停止它")
                self.session.stopRunning()
            }
            
            self.session.beginConfiguration()
            DebugLogger.debug("会话配置开始")
            
            // 检查相机权限
            let authStatus = AVCaptureDevice.authorizationStatus(for: .video)
            DebugLogger.debug("相机权限状态: \(authStatus.rawValue)")
            
            switch authStatus {
            case .authorized:
                DebugLogger.info("已获得相机权限")
                // 继续设置相机
                self.configureSession()
                
            case .notDetermined:
                DebugLogger.info("相机权限未确定，请求权限")
                
                // 更新UI状态
                DispatchQueue.main.async {
                    self.debugState = "请求相机权限..."
                }
                
                // 请求权限（这会显示系统对话框）
                AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                    guard let self = self else { return }
                    
                    if granted {
                        DebugLogger.info("用户授予了相机权限")
                        DispatchQueue.main.async {
                            self.debugState = "已获得相机权限，重新初始化..."
                        }
                        // 权限获取后重新调用设置
                        self.setupAndStartSession()
                    } else {
                        DebugLogger.error("用户拒绝了相机权限")
                        DispatchQueue.main.async {
                            self.sessionError = "相机权限被拒绝"
                            self.debugState = "相机权限被拒绝"
                        }
                        self.session.commitConfiguration()
                    }
                }
                return
                
            case .denied:
                DebugLogger.error("相机权限被拒绝")
                DispatchQueue.main.async {
                    self.sessionError = "相机权限被拒绝，请在设置中允许访问相机"
                    self.debugState = "相机权限被拒绝"
                }
                self.session.commitConfiguration()
                return
                
            case .restricted:
                DebugLogger.error("相机访问被限制")
                DispatchQueue.main.async {
                    self.sessionError = "相机访问受到限制，可能因为家长控制等原因"
                    self.debugState = "相机访问受限"
                }
                self.session.commitConfiguration()
                return
                
            @unknown default:
                DebugLogger.error("未知的相机权限状态")
                DispatchQueue.main.async {
                    self.sessionError = "未知的相机权限状态"
                    self.debugState = "权限状态异常"
                }
                self.session.commitConfiguration()
                return
            }
        }
    }
    
    /// 开始相机会话
    func startSession() {
        DebugLogger.info("开始启动相机会话")
        
        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在启动相机..."
        }
        
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            if self.session.isRunning {
                DebugLogger.warning("会话已经在运行中，无需重新启动")
                
                DispatchQueue.main.async { [weak self] in
                    self?.debugState = "相机已在运行中"
                }
                return
            }
            
            if self.isSessionInterrupted {
                DebugLogger.warning("会话当前被中断，无法启动")
                
                DispatchQueue.main.async { [weak self] in
                    self?.debugState = "相机被中断，无法启动"
                }
                return
            }
            
            DebugLogger.debug("在sessionQueue中启动会话")
            self.session.startRunning()
            
            // 检查启动是否成功
            let isRunning = self.session.isRunning
            DebugLogger.debug("会话启动结果: \(isRunning ? "成功" : "失败")")
            
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.isSessionRunning = isRunning
                
                if isRunning {
                    self.debugState = "相机已启动"
                } else {
                    let errorMsg = "相机会话无法启动"
                    self.sessionError = errorMsg
                    self.debugState = errorMsg
                }
            }
        }
    }
    
    /// 停止相机会话
    func stopSession() {
        DebugLogger.info("停止相机会话")
        
        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在停止相机..."
        }
        
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            if !self.session.isRunning {
                DebugLogger.warning("会话已经停止，无需再次停止")
                
                DispatchQueue.main.async { [weak self] in
                    self?.debugState = "相机已停止"
                }
                return
            }
            
            DebugLogger.debug("在sessionQueue中停止会话")
            self.session.stopRunning()
            
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.isSessionRunning = false
                self.debugState = "相机已停止"
            }
        }
    }
    
    // MARK: - 相机配置
    
    /// 配置相机会话（内部方法）
    private func configureSession() {
        DebugLogger.debug("开始配置相机会话")
        
        // 清除现有的输入和输出
        DebugLogger.debug("移除现有的输入和输出")
        for input in session.inputs {
            session.removeInput(input)
        }
        
        for output in session.outputs {
            session.removeOutput(output)
        }
        
        // 1. 设置摄像头输入
        DebugLogger.debug("设置摄像头输入，当前选择\(isUsingFrontCamera ? "前置" : "后置")摄像头")
        let position: AVCaptureDevice.Position = isUsingFrontCamera ? .front : .back
        
        // 获取摄像头设备
        guard let videoDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: position) else {
            let errorMsg = "无法获取\(position == .front ? "前置" : "后置")摄像头设备"
            DebugLogger.error(errorMsg)
            
            // 尝试获取任何可用的摄像头设备
            if let anyCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .unspecified) {
                DebugLogger.info("找到替代摄像头设备: \(anyCamera.localizedName)")
                configureCameraInput(anyCamera)
            } else {
                DebugLogger.error("设备上没有可用的摄像头")
                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
                session.commitConfiguration()
            }
            return
        }
        
        DebugLogger.info("成功获取摄像头设备: \(videoDevice.localizedName)")
        configureCameraInput(videoDevice)
    }
    
    /// 配置摄像头输入（辅助方法）
    private func configureCameraInput(_ videoDevice: AVCaptureDevice) {
        do {
            // 创建设备输入
            let videoDeviceInput = try AVCaptureDeviceInput(device: videoDevice)
            
            // 检查是否可以添加到会话
            if session.canAddInput(videoDeviceInput) {
                session.addInput(videoDeviceInput)
                DebugLogger.debug("成功添加视频输入到会话")
                configureVideoOutput() // 继续配置输出
            } else {
                let errorMsg = "无法将摄像头输入添加到会话"
                DebugLogger.error(errorMsg)
                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
                session.commitConfiguration()
            }
        } catch {
            let errorMsg = "创建摄像头输入时发生错误: \(error.localizedDescription)"
            DebugLogger.error(errorMsg)
            DispatchQueue.main.async { [weak self] in
                self?.sessionError = errorMsg
                self?.debugState = errorMsg
            }
            session.commitConfiguration()
        }
    }
    
    /// 配置视频输出（辅助方法）
    private func configureVideoOutput() {
        DebugLogger.debug("配置视频输出")
        
        // 添加视频输出
        if session.canAddOutput(output) {
            session.addOutput(output)
            // 配置输出格式
            output.videoSettings = [String(kCVPixelBufferPixelFormatTypeKey): kCMPixelFormat_32BGRA]
            output.alwaysDiscardsLateVideoFrames = true
            output.setSampleBufferDelegate(self, queue: sampleBufferQueue)
            DebugLogger.debug("成功添加视频输出到会话")
        } else {
            DebugLogger.warning("无法将视频输出添加到会话（这可能不是致命错误）")
        }
        
        // 创建预览层并完成配置
        finalizeConfiguration()
    }
    
    /// 完成会话配置（辅助方法）
    private func finalizeConfiguration() {
        DebugLogger.debug("完成会话配置")
        
        // 创建预览层
        let newPreviewLayer = AVCaptureVideoPreviewLayer(session: session)
        newPreviewLayer.videoGravity = .resizeAspectFill
        
        DebugLogger.debug("创建了新的预览层，关联到会话")
        
        // 提交会话配置
        DebugLogger.debug("提交会话配置")
        session.commitConfiguration()
        
        // 在主线程更新UI状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            DebugLogger.debug("在主线程更新UI状态")
            
            // 发布预览层
            self.preview = newPreviewLayer
            self.debugState = "相机初始化完成，准备启动"
            
            // 如果预览层创建成功，在延迟短暂时间后启动会话
            // 这个延迟可以确保SwiftUI视图有足够时间将预览层添加到其视图层次结构中
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                
                // 如果会话没有运行且没有被中断，则启动它
                if !self.session.isRunning && !self.isSessionInterrupted {
                    DebugLogger.debug("延迟后开始启动相机会话")
                    self.debugState = "正在启动相机..."
                    
                    // 在sessionQueue上启动会话
                    self.sessionQueue.async { [weak self] in
                        guard let self = self else { return }
                        
                        // 启动前检查会话状态
                        if self.session.isRunning {
                            DebugLogger.warning("会话已经在运行，无需重复启动")
                            return
                        }
                        
                        DebugLogger.debug("开始启动摄像头会话")
                        self.session.startRunning()
                        
                        // 验证会话是否成功启动
                        DispatchQueue.main.async { [weak self] in
                            guard let self = self else { return }
                            self.isSessionRunning = self.session.isRunning
                            
                            if self.isSessionRunning {
                                DebugLogger.info("摄像头会话成功启动")
                                self.debugState = "相机已启动"
                            } else {
                                let errorMsg = "摄像头会话启动失败"
                                DebugLogger.error(errorMsg)
                                self.sessionError = errorMsg
                                self.debugState = errorMsg
                            }
                        }
                    }
                } else {
                    if self.session.isRunning {
                        DebugLogger.warning("会话已经在运行中")
                    }
                    if self.isSessionInterrupted {
                        DebugLogger.warning("会话当前被中断，无法启动")
                    }
                }
            }
        }
    }
}
