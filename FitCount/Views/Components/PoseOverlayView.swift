import SwiftUI
import Foundation
import UIKit
import MediaPipeTasksVision

// MARK: - 人体姿态绘制相关组件

/// 人体姿态连接线结构
/// 表示两个关键点之间的直线连接
struct PoseLine {
    let from: CGPoint  // 起始点坐标
    let to: CGPoint    // 结束点坐标
}

/// 人体姿态叠加数据结构
/// 包含用于绘制的所有关键点和连接线信息
struct PoseOverlayData {
    let dots: [CGPoint]    // 关键点坐标数组
    let lines: [PoseLine]  // 连接线数组
}

/// 人体姿态叠加视图
/// 用于在相机预览上方绘制人体关键点和连接线的自定义UIView
class PoseOverlayView: UIView {

    /// 姿态叠加数据，设置时会触发重绘
    var poseOverlayData: PoseOverlayData? {
        didSet {
            setNeedsDisplay()
        }
    }

    // MARK: - 私有属性

    /// 内容图像尺寸
    private var contentImageSize: CGSize = CGSize.zero

    /// 图像内容模式
    var imageContentMode: UIView.ContentMode = .scaleAspectFit

    /// 设备方向
    private var orientation = UIDeviceOrientation.portrait

    /// 设备方向变化通知观察者
    private var orientationObserver: NSObjectProtocol?

    // MARK: - 初始化方法

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    /// 设置视图基本属性
    private func setupView() {
        backgroundColor = UIColor.clear
        isOpaque = false

        // 设置设备方向变化监听
        setupOrientationObserver()
    }

    /// 设置设备方向变化监听器
    private func setupOrientationObserver() {
        // 移除之前的观察者（如果存在）
        if let observer = orientationObserver {
            NotificationCenter.default.removeObserver(observer)
        }

        // 添加设备方向变化监听
        orientationObserver = NotificationCenter.default.addObserver(
            forName: UIDevice.orientationDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleOrientationChange()
        }

        DebugLogger.debug("已设置设备方向变化监听器")
    }

    /// 处理设备方向变化
    private func handleOrientationChange() {
        let newOrientation = UIDevice.current.orientation

        // 只在方向真正改变时才处理
        guard newOrientation != orientation else { return }

        let oldOrientation = orientation
        orientation = newOrientation

        // 记录方向变化
        let orientationName = getOrientationName(newOrientation)
        DebugLogger.debug("设备方向已改变: \(getOrientationName(oldOrientation)) -> \(orientationName)")

        // 如果有姿态数据，重新绘制以适应新方向
        if poseOverlayData != nil {
            DebugLogger.debug("重新绘制姿态叠加层以适应新方向")
            setNeedsDisplay()
        }
    }

    /// 获取方向的本地化名称
    private func getOrientationName(_ orientation: UIDeviceOrientation) -> String {
        switch orientation {
        case .portrait:
            return NSLocalizedString("orientation.portrait", comment: "竖屏")
        case .landscapeLeft:
            return NSLocalizedString("orientation.landscape.left", comment: "横屏左")
        case .landscapeRight:
            return NSLocalizedString("orientation.landscape.right", comment: "横屏右")
        default:
            return NSLocalizedString("orientation.unknown", comment: "未知方向")
        }
    }

    /// 清理资源
    deinit {
        // 移除方向变化观察者
        if let observer = orientationObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        DebugLogger.debug("PoseOverlayView 已销毁，清理方向监听器")
    }

    // MARK: - 公共方法

    /// 更新叠加层数据
    /// - Parameters:
    ///   - poseOverlayData: 新的姿态数据
    ///   - imageSize: 图像尺寸
    ///   - imageContentMode: 图像内容模式
    func updateOverlay(
        poseOverlayData: PoseOverlayData?,
        inBoundsOfContentImageOfSize imageSize: CGSize,
        imageContentMode: UIView.ContentMode) {

        self.clear()
        self.contentImageSize = imageSize
        self.poseOverlayData = poseOverlayData
        self.imageContentMode = imageContentMode
        self.orientation = UIDevice.current.orientation
        self.setNeedsDisplay()
    }

    /// 清除叠加层数据
    func clear() {
        poseOverlayData = nil
        contentImageSize = CGSize.zero
        imageContentMode = .scaleAspectFit
        orientation = UIDevice.current.orientation
        setNeedsDisplay()
    }

    // MARK: - 绘制方法

    override func draw(_ rect: CGRect) {
        guard let overlayData = poseOverlayData else { return }

        // 先绘制连接线，再绘制关键点，确保点在线的上方
        drawLines(overlayData.lines)
        drawDots(overlayData.dots)
    }

    /// 绘制关键点
    /// - Parameter dots: 关键点坐标数组
    private func drawDots(_ dots: [CGPoint]) {
        for dot in dots {
            let dotRect = CGRect(
                x: CGFloat(dot.x) - DefaultConstants.pointRadius / 2,
                y: CGFloat(dot.y) - DefaultConstants.pointRadius / 2,
                width: DefaultConstants.pointRadius,
                height: DefaultConstants.pointRadius)

            let path = UIBezierPath(ovalIn: dotRect)
            DefaultConstants.pointFillColor.setFill()
            DefaultConstants.pointColor.setStroke()
            path.stroke()
            path.fill()
        }
    }

    /// 绘制连接线
    /// - Parameter lines: 连接线数组
    private func drawLines(_ lines: [PoseLine]) {
        let path = UIBezierPath()
        for line in lines {
            path.move(to: line.from)
            path.addLine(to: line.to)
        }
        path.lineWidth = DefaultConstants.lineWidth
        DefaultConstants.lineColor.setStroke()
        path.stroke()
    }
}

// MARK: - 静态工具方法扩展

extension PoseOverlayView {

    /// 计算偏移量和缩放因子
    /// - Parameters:
    ///   - imageSize: 原始图像尺寸
    ///   - viewSize: 视图尺寸
    ///   - contentMode: 内容模式
    /// - Returns: 包含x偏移、y偏移和缩放因子的元组
    static func offsetsAndScaleFactor(
        forImageOfSize imageSize: CGSize,
        tobeDrawnInViewOfSize viewSize: CGSize,
        withContentMode contentMode: UIView.ContentMode)
    -> (xOffset: CGFloat, yOffset: CGFloat, scaleFactor: Double) {

        let widthScale = viewSize.width / imageSize.width
        let heightScale = viewSize.height / imageSize.height

        var scaleFactor = 0.0

        switch contentMode {
        case .scaleAspectFill:
            scaleFactor = max(widthScale, heightScale)
        case .scaleAspectFit:
            scaleFactor = min(widthScale, heightScale)
        default:
            scaleFactor = 1.0
        }

        let scaledSize = CGSize(
            width: imageSize.width * scaleFactor,
            height: imageSize.height * scaleFactor)
        let xOffset = (viewSize.width - scaledSize.width) / 2
        let yOffset = (viewSize.height - scaledSize.height) / 2

        return (xOffset, yOffset, scaleFactor)
    }

    /// 从MediaPipe关键点创建姿态叠加数据
    /// - Parameters:
    ///   - landmarks: MediaPipe检测到的关键点数组
    ///   - originalImageSize: 原始图像尺寸
    ///   - overlayViewSize: 叠加视图尺寸
    ///   - imageContentMode: 图像内容模式
    ///   - orientation: 图像方向
    /// - Returns: 转换后的姿态叠加数据，如果无关键点则返回nil
    static func createPoseOverlayData(
        fromPoseLandmarks landmarks: [NormalizedLandmark],
        inferredOnImageOfSize originalImageSize: CGSize,
        overlayViewSize: CGSize,
        imageContentMode: UIView.ContentMode,
        andOrientation orientation: UIImage.Orientation) -> PoseOverlayData? {

        guard !landmarks.isEmpty else { return nil }

        // 记录坐标转换过程，便于调试
        DebugLogger.debug("开始姿态坐标转换 - 原始图像尺寸: \(originalImageSize), 叠加视图尺寸: \(overlayViewSize), 方向: \(orientation)")

        let offsetsAndScaleFactor = PoseOverlayView.offsetsAndScaleFactor(
            forImageOfSize: originalImageSize,
            tobeDrawnInViewOfSize: overlayViewSize,
            withContentMode: imageContentMode)

        var transformedPoseLandmarks: [CGPoint]!

        // 根据MediaPipe坐标系统和iOS设备方向进行精确的坐标变换
        // MediaPipe返回标准化坐标(0-1)，其中：
        // - (0,0) 是左上角
        // - (1,1) 是右下角
        //
        // 重要说明：
        // 1. iOS相机传感器通常是横向的（landscape），但显示可能是纵向的（portrait）
        // 2. MediaPipe的坐标系统是基于输入图像的，需要根据实际显示方向进行转换
        // 3. 前置摄像头和后置摄像头的坐标系统可能不同，需要考虑镜像效果
        switch orientation {
        case .left:
            // 设备向左横屏（逆时针旋转90度）
            // 坐标变换：(x,y) -> (y, 1-x)
            transformedPoseLandmarks = landmarks.map({
                CGPoint(x: CGFloat($0.y), y: 1 - CGFloat($0.x))
            })
            DebugLogger.debug("应用左横屏坐标变换")

        case .right:
            // 设备向右横屏（顺时针旋转90度）
            // 坐标变换：(x,y) -> (1-y, x)
            transformedPoseLandmarks = landmarks.map({
                CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x))
            })
            DebugLogger.debug("应用右横屏坐标变换")

        case .up:
            // 设备竖屏（正常方向）
            // 对于竖屏模式，相机传感器通常是横向的，需要旋转坐标
            // 坐标变换：(x,y) -> (1-y, x) - 这样可以正确映射到竖屏显示
            transformedPoseLandmarks = landmarks.map({
                CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x))
            })
            DebugLogger.debug("应用竖屏坐标变换")

        case .down:
            // 设备倒置竖屏（旋转180度）
            // 坐标变换：(x,y) -> (y, 1-x)
            transformedPoseLandmarks = landmarks.map({
                CGPoint(x: CGFloat($0.y), y: 1 - CGFloat($0.x))
            })
            DebugLogger.debug("应用倒置竖屏坐标变换")

        default:
            // 默认情况：使用竖屏变换
            transformedPoseLandmarks = landmarks.map({
                CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x))
            })
            DebugLogger.debug("应用默认坐标变换（竖屏模式）")
        }

        // 将标准化坐标转换为视图坐标
        let dots: [CGPoint] = transformedPoseLandmarks.map({
            CGPoint(
                x: CGFloat($0.x) * originalImageSize.width * offsetsAndScaleFactor.scaleFactor + offsetsAndScaleFactor.xOffset,
                y: CGFloat($0.y) * originalImageSize.height * offsetsAndScaleFactor.scaleFactor + offsetsAndScaleFactor.yOffset
            )
        })

        // 使用MediaPipe姿态连接创建连接线
        let lines: [PoseLine] = PoseLandmarker.poseLandmarks.map({ connection in
            let start = dots[Int(connection.start)]
            let end = dots[Int(connection.end)]
            return PoseLine(from: start, to: end)
        })

        return PoseOverlayData(dots: dots, lines: lines)
    }
}

// MARK: - SwiftUI包装器

/// SwiftUI视图代表 - 包装PoseOverlayView
struct PoseOverlayViewRepresentable: UIViewRepresentable {
    var poseOverlayView: PoseOverlayView

    func makeUIView(context: Context) -> PoseOverlayView {
        DebugLogger.debug("PoseOverlayViewRepresentable.makeUIView被调用")
        return poseOverlayView
    }

    func updateUIView(_ uiView: PoseOverlayView, context: Context) {
        DebugLogger.verbose("PoseOverlayViewRepresentable.updateUIView被调用")
        // PoseOverlayView 会自动更新，无需额外操作
    }

    static func dismantleUIView(_ uiView: PoseOverlayView, coordinator: ()) {
        DebugLogger.debug("PoseOverlayViewRepresentable.dismantleUIView被调用")
        uiView.clear()
    }
}
